# SDPM Chunker 文档分块教程

## 什么是SDPM Chunker？

可能就是长文档，那种内容分布在页面不同地方的pdf有点用。

SDPM Chunker（Semantic Double-Pass Merging Chunker）是一种高级的语义分块器，它使用双重语义合并方法来改善上下文保持。它首先通过语义相似性对内容进行分组，然后在跳跃窗口内合并相似的组，这样可以连接那些在文本中可能不连续但相关的内容。

这种技术特别适用于具有重复主题或概念分散在文档各处的文档。

## 安装依赖

首先，你需要安装带有语义功能的chonkie包：

```bash
pip install "chonkie[semantic]"
```

## 基本使用方法

### 1. 导入和初始化

```python
from chonkie import SDPMChunker

# 基本初始化，使用默认参数
chunker = SDPMChunker(
    embedding_model="minishlab/potion-base-8M",  # 默认模型
    threshold=0.5,                              # 相似度阈值 (0-1)
    chunk_size=2048,                            # 每个块的最大token数
    min_sentences=1,                            # 每个块的最小句子数
    skip_window=1                               # 寻找相似性时跳过的块数
)
```

### 2. 读取txt文档并分块

```python
def chunk_txt_file(file_path, chunker):
    """
    读取txt文件并使用SDPM Chunker进行分块
    
    Args:
        file_path: txt文件路径
        chunker: SDPM Chunker实例
    
    Returns:
        chunks: 分块结果列表
    """
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as file:
        text = file.read()
    
    # 进行分块
    chunks = chunker.chunk(text)
    
    return chunks

def print_all_chunks(chunks):
    """
    打印所有分块的内容
    
    Args:
        chunks: 分块结果列表
    """
    print(f"总共生成了 {len(chunks)} 个分块\n")
    print("=" * 80)
    
    for i, chunk in enumerate(chunks, 1):
        print(f"分块 {i}:")
        print(f"文本内容: {chunk.text}")
        print(f"Token数量: {chunk.token_count}")
        print(f"句子数量: {len(chunk.sentences)}")
        print(f"开始位置: {chunk.start_index}")
        print(f"结束位置: {chunk.end_index}")
        print("-" * 80)
```

### 3. 完整示例代码

```python
from chonkie import SDPMChunker

def main():
    # 初始化SDPM Chunker
    chunker = SDPMChunker(
        embedding_model="minishlab/potion-base-8M",
        threshold=0.5,
        chunk_size=2048,
        min_sentences=1,
        skip_window=1
    )
    
    # 指定你的txt文件路径
    file_path = "your_document.txt"  # 替换为你的文件路径
    
    try:
        # 读取并分块
        chunks = chunk_txt_file(file_path, chunker)
        
        # 打印所有分块
        print_all_chunks(chunks)
        
        # 额外信息：打印每个分块中的句子详情
        print("\n详细句子信息:")
        print("=" * 80)
        for i, chunk in enumerate(chunks, 1):
            print(f"\n分块 {i} 的句子:")
            for j, sentence in enumerate(chunk.sentences, 1):
                print(f"  句子 {j}: {sentence.text}")
                print(f"    Token数: {sentence.token_count}")
                if hasattr(sentence, 'embedding') and sentence.embedding is not None:
                    print(f"    嵌入向量维度: {sentence.embedding.shape}")
    
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
    except Exception as e:
        print(f"处理过程中出现错误: {e}")

if __name__ == "__main__":
    main()
```

## 参数详解

### 核心参数

- **embedding_model**: 嵌入模型，默认为"minishlab/potion-base-8M"
- **threshold**: 相似度阈值，范围0-1，用于判断句子是否相似
- **chunk_size**: 每个块的最大token数，默认2048
- **skip_window**: 寻找相似性时跳过的块数，默认1

### 高级参数

- **mode**: 句子分组模式，"cumulative"或"window"
- **min_sentences**: 每个块的最小句子数
- **min_chunk_size**: 每个块的最小token数
- **similarity_window**: 用于相似度阈值计算的句子数
- **delim**: 句子分割符，默认为['.', '!', '?', '\n']

## 批量处理多个文件

```python
def batch_chunk_files(file_paths, chunker):
    """
    批量处理多个txt文件
    
    Args:
        file_paths: 文件路径列表
        chunker: SDPM Chunker实例
    
    Returns:
        all_chunks: 所有文件的分块结果
    """
    texts = []
    
    # 读取所有文件
    for file_path in file_paths:
        with open(file_path, 'r', encoding='utf-8') as file:
            texts.append(file.read())
    
    # 批量分块
    batch_chunks = chunker.chunk_batch(texts)
    
    return batch_chunks

# 使用示例
file_paths = ["doc1.txt", "doc2.txt", "doc3.txt"]
batch_results = batch_chunk_files(file_paths, chunker)

for i, doc_chunks in enumerate(batch_results):
    print(f"\n文档 {i+1} 的分块结果:")
    print_all_chunks(doc_chunks)
```

## 返回的数据结构

SDPM Chunker返回`SemanticChunk`对象，包含以下属性：

- **text**: 分块的文本内容
- **start_index**: 在原文中的开始位置
- **end_index**: 在原文中的结束位置  
- **token_count**: 该分块的token数量
- **sentences**: 包含的句子列表（SemanticSentence对象）

每个句子对象包含：
- **text**: 句子文本
- **token_count**: 句子的token数
- **embedding**: 句子的嵌入向量（如果可用）

## 使用建议

1. **选择合适的阈值**: 较低的阈值会产生更多、更小的块；较高的阈值会产生更少、更大的块
2. **调整chunk_size**: 根据你的应用需求调整最大块大小
3. **考虑skip_window**: 增大skip_window可以捕获更远距离的相关内容
4. **文件编码**: 确保txt文件使用UTF-8编码以避免读取错误

## 注意事项

- SDPM Chunker需要下载嵌入模型，首次使用时可能需要一些时间
- 处理大文件时，分块过程可能需要较长时间
- 确保有足够的内存来处理大型文档和嵌入向量

这个教程应该能帮助你开始使用SDPM Chunker对txt文档进行智能分块！
