# SDPM Chunker 使用指南

这个项目演示如何使用Chonkie的SDPM Chunker对txt文档进行智能分块。

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install "chonkie[semantic]"
```

### 2. 运行简单测试

```bash
python simple_chunker_test.py
```

### 3. 处理你自己的txt文件

```bash
python simple_chunker_test.py your_document.txt
```

### 4. 运行完整示例

```bash
python sdpm_chunker_example.py
```

或者处理指定文件：

```bash
python sdpm_chunker_example.py your_document.txt
```

## 📁 文件说明

- `SDPM_Chunker_教程.md` - 详细的使用教程和API说明
- `sdpm_chunker_example.py` - 完整的示例代码，包含详细输出
- `simple_chunker_test.py` - 简化版测试代码，快速验证功能
- `README.md` - 本说明文件

## 🔧 主要功能

- **智能分块**: 使用语义双重合并算法进行文档分块
- **上下文保持**: 能够连接相关但不连续的内容
- **详细输出**: 显示每个分块的详细信息
- **批量处理**: 支持同时处理多个文件
- **灵活配置**: 可调整分块大小、相似度阈值等参数

## 📊 输出信息

每个分块包含：
- 文本内容
- Token数量
- 句子数量
- 在原文中的位置
- 句子级别的详细信息

## ⚙️ 参数调优

主要参数：
- `threshold`: 相似度阈值 (0-1)
- `chunk_size`: 最大分块大小
- `skip_window`: 跳跃窗口大小
- `min_sentences`: 最小句子数

## 🎯 使用场景

- 长文档的智能分割
- 内容相关性分析
- 文档预处理
- RAG系统的文档准备

## 💡 提示

- 首次运行会下载嵌入模型，需要一些时间
- 确保txt文件使用UTF-8编码
- 大文件处理可能需要较长时间
- 可以根据需要调整参数以获得最佳效果
