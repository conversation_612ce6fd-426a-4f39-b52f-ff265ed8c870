#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解释为什么SDPM在某些情况下看起来"没什么用"
"""

from chonkie import SDPMChunker, SentenceChunker, TokenChunker

def explain_your_confusion():
    """解释你遇到的情况"""
    
    print("🤔 为什么你觉得SDPM没什么用？")
    print("="*50)
    
    print("\n📄 你的文档特点:")
    print("• 是一篇连续的随笔/思考")
    print("• 虽然都在讲AI，但是是流水账式的")
    print("• 没有重复的概念需要合并")
    print("• 内容本身就是连贯的")
    
    print("\n🎯 SDPM的设计目的:")
    print("• 处理主题分散但相关的文档")
    print("• 合并距离较远但语义相关的内容")
    print("• 保持概念的完整性")
    
    print("\n💡 问题所在:")
    print("你的文档类型不适合展示SDPM的优势！")
    print("就像用法拉利在拥堵的市区开车，")
    print("看不出它比普通车好在哪里。")

def create_sdpm_showcase():
    """创建一个能展示SDPM优势的例子"""
    
    print(f"\n{'='*60}")
    print("🚀 SDPM真正的用武之地")
    print("="*60)
    
    # 这种文档SDPM会很有用
    technical_doc = """
    机器学习基础概念介绍。
    
    数据预处理是第一步。需要清洗和标准化数据。
    
    Python是最流行的编程语言之一。
    
    监督学习需要标注数据。分类和回归是主要任务。
    
    JavaScript用于前端开发。
    
    特征工程对模型性能很重要。好的特征能提升准确率。
    
    HTML是网页的基础结构。
    
    无监督学习不需要标注。聚类是常见应用。
    
    CSS负责网页样式。
    
    模型评估使用交叉验证。避免过拟合很关键。
    
    React是流行的前端框架。
    
    深度学习使用神经网络。多层结构能学习复杂模式。
    """
    
    print("📄 测试文档: 技术概念混合文档")
    print("(机器学习概念和Web开发概念混在一起)")
    
    # 对比不同分块器
    chunkers = {
        "普通句子分块": SentenceChunker(chunk_size=100),
        "SDPM智能分块": SDPMChunker(chunk_size=100, threshold=0.6, skip_window=2)
    }
    
    for name, chunker in chunkers.items():
        print(f"\n🔧 {name}:")
        print("-" * 30)
        
        chunks = chunker.chunk(technical_doc)
        
        for i, chunk in enumerate(chunks, 1):
            print(f"\n  分块 {i}:")
            lines = [line.strip() for line in chunk.text.split('\n') if line.strip()]
            for line in lines:
                print(f"    • {line}")
            
            # 分析主题
            ml_count = sum(1 for line in lines if any(word in line for word in 
                          ["机器学习", "监督", "无监督", "特征", "模型", "深度学习", "神经网络"]))
            web_count = sum(1 for line in lines if any(word in line for word in 
                           ["Python", "JavaScript", "HTML", "CSS", "React"]))
            data_count = sum(1 for line in lines if any(word in line for word in 
                            ["数据", "预处理", "清洗"]))
            
            topics = []
            if ml_count > 0: topics.append(f"机器学习({ml_count})")
            if web_count > 0: topics.append(f"Web开发({web_count})")  
            if data_count > 0: topics.append(f"数据处理({data_count})")
            
            if topics:
                print(f"    🎯 主题分布: {', '.join(topics)}")

def show_real_world_example():
    """展示真实世界的应用场景"""
    
    print(f"\n{'='*60}")
    print("🌍 SDPM的真实应用场景")
    print("="*60)
    
    scenarios = [
        {
            "场景": "📚 技术文档",
            "描述": "API文档中，同一个概念在多个章节出现",
            "SDPM优势": "将相关的API说明合并在一起"
        },
        {
            "场景": "📊 研究报告", 
            "描述": "实验数据分散在不同章节",
            "SDPM优势": "将相关实验结果组合分析"
        },
        {
            "场景": "📖 教材内容",
            "描述": "同一概念在不同章节有补充说明",
            "SDPM优势": "保持概念的完整性"
        },
        {
            "场景": "🗂️ 知识库",
            "描述": "相关问题的答案分散在各处",
            "SDPM优势": "将相关QA组合在一起"
        },
        {
            "场景": "📝 你的随笔",
            "描述": "连续的思考流，内容本身连贯",
            "SDPM优势": "❌ 没有明显优势，普通分块器就够了"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n{scenario['场景']}: {scenario['描述']}")
        print(f"   💡 {scenario['SDPM优势']}")

def conclusion():
    """总结"""
    
    print(f"\n{'='*60}")
    print("🎯 总结")
    print("="*60)
    
    print("\n🤔 你的困惑很正常！")
    print("因为你选择的测试文档不适合展示SDPM的优势。")
    
    print("\n📄 你的文档特点:")
    print("• 连续叙述，内容本身连贯")
    print("• 没有重复概念需要合并")
    print("• 适合用普通的SentenceChunker")
    
    print("\n🚀 SDPM适合的文档:")
    print("• 技术手册 (概念分散)")
    print("• 学术论文 (相关内容在不同段落)")
    print("• 知识库 (相关信息需要组合)")
    print("• 复杂报告 (同一主题多次出现)")
    
    print("\n💡 建议:")
    print("• 对于你的随笔类文档，用SentenceChunker就够了")
    print("• 如果要测试SDPM，试试技术文档或学术论文")
    print("• 不是所有工具都适合所有场景")
    
    print("\n🎭 类比:")
    print("这就像用专业相机拍证件照，")
    print("虽然相机很高级，但优势体现不出来。")
    print("要拍风景大片才能看出差别！")

if __name__ == "__main__":
    explain_your_confusion()
    create_sdpm_showcase()
    show_real_world_example()
    conclusion()
