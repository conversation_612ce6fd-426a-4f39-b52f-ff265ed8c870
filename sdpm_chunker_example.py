#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SDPM Chunker 示例代码
使用语义双重合并分块器对txt文档进行智能分块
"""

from chonkie import SDPMChunker
import os
import sys

def create_sample_text_file():
    """创建一个示例txt文件用于测试"""
    sample_text = """人工智能是计算机科学的一个分支，它试图理解智能的实质。
机器学习是人工智能的一个重要子领域。
深度学习使用神经网络来模拟人脑的工作方式。

神经网络通过多层处理来学习复杂的模式。
训练数据对于模型性能至关重要。
GPU显著加速神经网络的计算过程。

高质量的训练数据能够提高模型的准确性。
数据预处理是训练过程中的关键步骤。
TPU为深度学习提供了专门的硬件支持。

自然语言处理是人工智能的另一个重要应用领域。
计算机视觉让机器能够理解和分析图像。
强化学习通过奖励机制来训练智能体。

机器学习算法可以从数据中自动学习模式。
监督学习需要标注的训练数据。
无监督学习可以发现数据中的隐藏结构。

深度学习在图像识别方面取得了突破性进展。
卷积神经网络特别适合处理图像数据。
循环神经网络擅长处理序列数据。

人工智能的发展正在改变各个行业。
自动驾驶汽车是人工智能的重要应用。
医疗诊断中的AI应用正在快速发展。"""
    
    with open("sample_document.txt", "w", encoding="utf-8") as f:
        f.write(sample_text)
    
    print("已创建示例文档: sample_document.txt")
    return "sample_document.txt"

def chunk_txt_file(file_path, chunker):
    """
    读取txt文件并使用SDPM Chunker进行分块
    
    Args:
        file_path: txt文件路径
        chunker: SDPM Chunker实例
    
    Returns:
        chunks: 分块结果列表
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            text = file.read()
        
        print(f"正在处理文件: {file_path}")
        print(f"文档长度: {len(text)} 字符")
        print("开始分块处理...\n")
        
        # 进行分块
        chunks = chunker.chunk(text)
        
        return chunks
    
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        return []
    except Exception as e:
        print(f"读取文件时出现错误: {e}")
        return []

def print_all_chunks(chunks):
    """
    打印所有分块的内容
    
    Args:
        chunks: 分块结果列表
    """
    if not chunks:
        print("没有生成任何分块")
        return
    
    print(f"总共生成了 {len(chunks)} 个分块")
    print("=" * 100)
    
    for i, chunk in enumerate(chunks, 1):
        print(f"\n📄 分块 {i}:")
        print(f"📝 文本内容: {chunk.text}")
        print(f"🔢 Token数量: {chunk.token_count}")
        print(f"📊 句子数量: {len(chunk.sentences)}")
        print(f"📍 开始位置: {chunk.start_index}")
        print(f"📍 结束位置: {chunk.end_index}")
        print("-" * 100)

def print_detailed_sentences(chunks):
    """
    打印每个分块中句子的详细信息
    
    Args:
        chunks: 分块结果列表
    """
    print("\n🔍 详细句子信息:")
    print("=" * 100)
    
    for i, chunk in enumerate(chunks, 1):
        print(f"\n📄 分块 {i} 的句子详情:")
        for j, sentence in enumerate(chunk.sentences, 1):
            print(f"  📝 句子 {j}: {sentence.text}")
            print(f"     🔢 Token数: {sentence.token_count}")
            print(f"     📍 位置: {sentence.start_index}-{sentence.end_index}")
            
            # 检查是否有嵌入向量
            if hasattr(sentence, 'embedding') and sentence.embedding is not None:
                print(f"     🧠 嵌入向量维度: {sentence.embedding.shape}")
            else:
                print(f"     🧠 嵌入向量: 未生成")
        print("-" * 80)

def batch_chunk_files(file_paths, chunker):
    """
    批量处理多个txt文件
    
    Args:
        file_paths: 文件路径列表
        chunker: SDPM Chunker实例
    
    Returns:
        all_chunks: 所有文件的分块结果
    """
    texts = []
    valid_files = []
    
    # 读取所有文件
    for file_path in file_paths:
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                texts.append(file.read())
                valid_files.append(file_path)
                print(f"✅ 成功读取: {file_path}")
        except Exception as e:
            print(f"❌ 读取失败: {file_path} - {e}")
    
    if not texts:
        print("没有成功读取任何文件")
        return []
    
    print(f"\n开始批量处理 {len(texts)} 个文件...")
    
    # 批量分块
    batch_chunks = chunker.chunk_batch(texts)
    
    return batch_chunks, valid_files

def main():
    """主函数"""
    print("🚀 SDPM Chunker 文档分块示例")
    print("=" * 50)
    
    # 初始化SDPM Chunker
    print("🔧 正在初始化SDPM Chunker...")
    try:
        chunker = SDPMChunker(
            embedding_model="minishlab/potion-base-8M",  # 默认模型
            threshold=0.5,                              # 相似度阈值
            chunk_size=512,                             # 较小的块大小用于演示
            min_sentences=1,                            # 最小句子数
            skip_window=1                               # 跳跃窗口
        )
        print("✅ SDPM Chunker 初始化成功!")
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        print("请确保已安装: pip install 'chonkie[semantic]'")
        return
    
    # 检查是否有命令行参数指定的文件
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return
    else:
        # 创建示例文件
        print("\n📝 创建示例文档...")
        file_path = create_sample_text_file()
    
    # 单文件处理
    print(f"\n📖 处理单个文件: {file_path}")
    chunks = chunk_txt_file(file_path, chunker)
    
    if chunks:
        # 打印分块结果
        print_all_chunks(chunks)
        
        # 打印详细句子信息
        print_detailed_sentences(chunks)
        
        # 统计信息
        total_tokens = sum(chunk.token_count for chunk in chunks)
        total_sentences = sum(len(chunk.sentences) for chunk in chunks)
        
        print(f"\n📊 统计信息:")
        print(f"   总分块数: {len(chunks)}")
        print(f"   总Token数: {total_tokens}")
        print(f"   总句子数: {total_sentences}")
        print(f"   平均每块Token数: {total_tokens/len(chunks):.1f}")
        print(f"   平均每块句子数: {total_sentences/len(chunks):.1f}")
    
    print("\n🎉 处理完成!")

if __name__ == "__main__":
    main()
