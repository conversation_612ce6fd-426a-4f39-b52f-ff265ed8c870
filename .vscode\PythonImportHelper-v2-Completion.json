[{"label": "<PERSON><PERSON><PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "detail": "<PERSON><PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "glob", "kind": 6, "isExtraImport": true, "importPath": "glob", "description": "glob", "detail": "glob", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "shutil", "kind": 6, "isExtraImport": true, "importPath": "shutil", "description": "shutil", "detail": "shutil", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "sysconfig", "kind": 6, "isExtraImport": true, "importPath": "sysconfig", "description": "sysconfig", "detail": "sysconfig", "documentation": {}}, {"label": "tempfile", "kind": 6, "isExtraImport": true, "importPath": "tempfile", "description": "tempfile", "detail": "tempfile", "documentation": {}}, {"label": "winreg", "kind": 6, "isExtraImport": true, "importPath": "winreg", "description": "winreg", "detail": "winreg", "documentation": {}}, {"label": "site", "kind": 6, "isExtraImport": true, "importPath": "site", "description": "site", "detail": "site", "documentation": {}}, {"label": "subprocess", "kind": 6, "isExtraImport": true, "importPath": "subprocess", "description": "subprocess", "detail": "subprocess", "documentation": {}}, {"label": "SDPMChunker", "importPath": "chonkie", "description": "chonkie", "isExtraImport": true, "detail": "chonkie", "documentation": {}}, {"label": "TokenChunker", "importPath": "chonkie", "description": "chonkie", "isExtraImport": true, "detail": "chonkie", "documentation": {}}, {"label": "SDPMChunker", "importPath": "chonkie", "description": "chonkie", "isExtraImport": true, "detail": "chonkie", "documentation": {}}, {"label": "SDPMChunker", "importPath": "chonkie", "description": "chonkie", "isExtraImport": true, "detail": "chonkie", "documentation": {}}, {"label": "TokenChunker", "importPath": "chonkie", "description": "chonkie", "isExtraImport": true, "detail": "chonkie", "documentation": {}}, {"label": "SentenceChunker", "importPath": "chonkie", "description": "chonkie", "isExtraImport": true, "detail": "chonkie", "documentation": {}}, {"label": "SDPMChunker", "importPath": "chonkie", "description": "chonkie", "isExtraImport": true, "detail": "chonkie", "documentation": {}}, {"label": "SDPMChunker", "importPath": "chonkie", "description": "chonkie", "isExtraImport": true, "detail": "chonkie", "documentation": {}}, {"label": "SentenceChunker", "importPath": "chonkie", "description": "chonkie", "isExtraImport": true, "detail": "chonkie", "documentation": {}}, {"label": "TokenChunker", "importPath": "chonkie", "description": "chonkie", "isExtraImport": true, "detail": "chonkie", "documentation": {}}, {"label": "tiktoken", "kind": 6, "isExtraImport": true, "importPath": "tiktoken", "description": "tiktoken", "detail": "tiktoken", "documentation": {}}, {"label": "<PERSON><PERSON>", "kind": 6, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "class Tee:\n    def __init__(self, file):\n        self.f = file\n    def write(self, what):\n        if self.f is not None:\n            try:\n                self.f.write(what.replace(\"\\n\", \"\\r\\n\"))\n            except OSError:\n                pass\n        tee_f.write(what)", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "get_root_hkey", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def get_root_hkey():\n    try:\n        winreg.OpenKey(\n            winreg.HKEY_LOCAL_MACHINE, root_key_name, 0, winreg.KEY_CREATE_SUB_KEY\n        )\n        return winreg.HKEY_LOCAL_MACHINE\n    except OSError:\n        # Either not exist, or no permissions to create subkey means\n        # must be HKCU\n        return winreg.HKEY_CURRENT_USER", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "create_shortcut", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def create_shortcut(\n    path, description, filename, arguments=\"\", workdir=\"\", iconpath=\"\", iconindex=0\n):\n    import pythoncom\n    from win32com.shell import shell\n    ilink = pythoncom.CoCreateInstance(\n        shell.CLSID_ShellLink,\n        None,\n        pythoncom.CLSCTX_INPROC_SERVER,\n        shell.IID_IShellLink,", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "get_special_folder_path", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def get_special_folder_path(path_name):\n    from win32com.shell import shell, shellcon\n    for maybe in \"\"\"\n        CSIDL_COMMON_STARTMENU CSIDL_STARTMENU CSIDL_COMMON_APPDATA\n        CSIDL_LOCAL_APPDATA CSIDL_APPDATA CSIDL_COMMON_DESKTOPDIRECTORY\n        CSIDL_DESKTOPDIRECTORY CSIDL_COMMON_STARTUP CSIDL_STARTUP\n        CSIDL_COMMON_PROGRAMS CSIDL_PROGRAMS CSIDL_PROGRAM_FILES_COMMON\n        CSIDL_PROGRAM_FILES CSIDL_FONTS\"\"\".split():\n        if maybe == path_name:\n            csidl = getattr(shellcon, maybe)", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "CopyTo", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def CopyTo(desc, src, dest):\n    import win32api\n    import win32con\n    while 1:\n        try:\n            win32api.CopyFile(src, dest, 0)\n            return\n        except win32api.error as details:\n            if details.winerror == 5:  # access denied - user not admin.\n                raise", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "LoadSystemModule", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def LoadSystemModule(lib_dir, modname):\n    # See if this is a debug build.\n    import importlib.machinery\n    import importlib.util\n    suffix = \"_d\" if \"_d.pyd\" in importlib.machinery.EXTENSION_SUFFIXES else \"\"\n    filename = \"%s%d%d%s.dll\" % (\n        modname,\n        sys.version_info.major,\n        sys.version_info.minor,\n        suffix,", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "SetPyKeyVal", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def SetPyKeyVal(key_name, value_name, value):\n    root_hkey = get_root_hkey()\n    root_key = winreg.OpenKey(root_hkey, root_key_name)\n    try:\n        my_key = winreg.CreateKey(root_key, key_name)\n        try:\n            winreg.SetValueEx(my_key, value_name, 0, winreg.REG_SZ, value)\n            if verbose:\n                print(f\"-> {root_key_name}\\\\{key_name}[{value_name}]={value!r}\")\n        finally:", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "UnsetPyKeyVal", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def UnsetPyKeyVal(key_name, value_name, delete_key=False):\n    root_hkey = get_root_hkey()\n    root_key = winreg.OpenKey(root_hkey, root_key_name)\n    try:\n        my_key = winreg.OpenKey(root_key, key_name, 0, winreg.KEY_SET_VALUE)\n        try:\n            winreg.DeleteValue(my_key, value_name)\n            if verbose:\n                print(f\"-> DELETE {root_key_name}\\\\{key_name}[{value_name}]\")\n        finally:", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "RegisterCOMObjects", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def RegisterCOMObjects(register=True):\n    import win32com.server.register\n    if register:\n        func = win32com.server.register.RegisterClasses\n    else:\n        func = win32com.server.register.UnregisterClasses\n    flags = {}\n    if not verbose:\n        flags[\"quiet\"] = 1\n    for module, klass_name in com_modules:", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "RegisterHelpFile", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def RegisterHelpFile(register=True, lib_dir=None):\n    if lib_dir is None:\n        lib_dir = sysconfig.get_paths()[\"platlib\"]\n    if register:\n        # Register the .chm help file.\n        chm_file = os.path.join(lib_dir, \"PyWin32.chm\")\n        if os.path.isfile(chm_file):\n            # This isn't recursive, so if 'Help' doesn't exist, we croak\n            SetPyKeyVal(\"Help\", None, None)\n            SetPyKeyVal(\"Help\\\\Pythonwin Reference\", None, chm_file)", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "RegisterPyt<PERSON><PERSON>", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def RegisterPythonwin(register=True, lib_dir=None):\n    \"\"\"Add (or remove) Pythonwin to context menu for python scripts.\n    ??? Should probably also add Edit command for pys files also.\n    Also need to remove these keys on uninstall, but there's no function\n    to add registry entries to uninstall log ???\n    \"\"\"\n    import os\n    if lib_dir is None:\n        lib_dir = sysconfig.get_paths()[\"platlib\"]\n    classes_root = get_root_hkey()", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "get_shortcuts_folder", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def get_shortcuts_folder():\n    if get_root_hkey() == winreg.HKEY_LOCAL_MACHINE:\n        try:\n            fldr = get_special_folder_path(\"CSIDL_COMMON_PROGRAMS\")\n        except OSError:\n            # No CSIDL_COMMON_PROGRAMS on this platform\n            fldr = get_special_folder_path(\"CSIDL_PROGRAMS\")\n    else:\n        # non-admin install - always goes in this user's start menu.\n        fldr = get_special_folder_path(\"CSIDL_PROGRAMS\")", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "get_system_dir", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def get_system_dir():\n    import win32api  # we assume this exists.\n    try:\n        import pythoncom\n        import win32process\n        from win32com.shell import shell, shellcon\n        try:\n            if win32process.IsWow64Process():\n                return shell.SHGetSpecialFolderPath(0, shellcon.CSIDL_SYSTEMX86)\n            return shell.SHGetSpecialFolderPath(0, shellcon.CSIDL_SYSTEM)", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "fixup_dbi", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def fixup_dbi():\n    # We used to have a dbi.pyd with our .pyd files, but now have a .py file.\n    # If the user didn't uninstall, they will find the .pyd which will cause\n    # problems - so handle that.\n    import win32api\n    import win32con\n    pyd_name = os.path.join(os.path.dirname(win32api.__file__), \"dbi.pyd\")\n    pyd_d_name = os.path.join(os.path.dirname(win32api.__file__), \"dbi_d.pyd\")\n    py_name = os.path.join(os.path.dirname(win32con.__file__), \"dbi.py\")\n    for this_pyd in (pyd_name, pyd_d_name):", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "install", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def install(lib_dir):\n    import traceback\n    # The .pth file is now installed as a regular file.\n    # Create the .pth file in the site-packages dir, and use only relative paths\n    # We used to write a .pth directly to sys.prefix - clobber it.\n    if os.path.isfile(os.path.join(sys.prefix, \"pywin32.pth\")):\n        os.unlink(os.path.join(sys.prefix, \"pywin32.pth\"))\n    # The .pth may be new and therefore not loaded in this session.\n    # Setup the paths just in case.\n    for name in \"win32 win32\\\\lib Pythonwin\".split():", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "uninstall", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def uninstall(lib_dir):\n    # First ensure our system modules are loaded from pywin32_system, so\n    # we can remove the ones we copied...\n    LoadSystemModule(lib_dir, \"pywintypes\")\n    LoadSystemModule(lib_dir, \"pythoncom\")\n    try:\n        RegisterCOMObjects(False)\n    except Exception as why:\n        print(f\"Failed to unregister COM objects: {why}\")\n    try:", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "verify_destination", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def verify_destination(location: str) -> str:\n    location = os.path.abspath(location)\n    if not os.path.isdir(location):\n        raise argparse.ArgumentTypeError(\n            f'Path \"{location}\" is not an existing directory!'\n        )\n    return location\ndef main():\n    parser = argparse.ArgumentParser(\n        formatter_class=argparse.RawDescriptionHelpFormatter,", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "main", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def main():\n    parser = argparse.ArgumentParser(\n        formatter_class=argparse.RawDescriptionHelpFormatter,\n        description=\"\"\"A post-install script for the pywin32 extensions.\n    * Typical usage:\n    > python -m pywin32_postinstall -install\n    * or (shorter but you don't have control over which python environment is used)\n    > pywin32_postinstall -install\n    You need to execute this script, with a '-install' parameter,\n    to ensure the environment is setup correctly to install COM objects, services, etc.", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "tee_f", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "tee_f = open(\n    os.path.join(\n        tempfile.gettempdir(),  # Send output somewhere so it can be found if necessary...\n        \"pywin32_postinstall.log\",\n    ),\n    \"w\",\n)\nclass Tee:\n    def __init__(self, file):\n        self.f = file", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "sys.stderr", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "sys.stderr = <PERSON><PERSON>(sys.stderr)\nsys.stdout = Tee(sys.stdout)\ncom_modules = [\n    # module_name,                      class_names\n    (\"win32com.servers.interp\", \"Interpreter\"),\n    (\"win32com.servers.dictionary\", \"DictionaryPolicy\"),\n    (\"win32com.axscript.client.pyscript\", \"PyScript\"),\n]\n# Is this a 'silent' install - ie, avoid all dialogs.\n# Different than 'verbose'", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "sys.stdout", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "sys.stdout = Tee(sys.stdout)\ncom_modules = [\n    # module_name,                      class_names\n    (\"win32com.servers.interp\", \"Interpreter\"),\n    (\"win32com.servers.dictionary\", \"DictionaryPolicy\"),\n    (\"win32com.axscript.client.pyscript\", \"PyScript\"),\n]\n# Is this a 'silent' install - ie, avoid all dialogs.\n# Different than 'verbose'\nsilent = 0", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "com_modules", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "com_modules = [\n    # module_name,                      class_names\n    (\"win32com.servers.interp\", \"Interpreter\"),\n    (\"win32com.servers.dictionary\", \"DictionaryPolicy\"),\n    (\"win32com.axscript.client.pyscript\", \"PyScript\"),\n]\n# Is this a 'silent' install - ie, avoid all dialogs.\n# Different than 'verbose'\nsilent = 0\n# Verbosity of output messages.", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "silent", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "silent = 0\n# Verbosity of output messages.\nverbose = 1\nroot_key_name = \"Software\\\\Python\\\\PythonCore\\\\\" + sys.winver\ndef get_root_hkey():\n    try:\n        winreg.OpenKey(\n            winreg.HKEY_LOCAL_MACHINE, root_key_name, 0, winreg.KEY_CREATE_SUB_KEY\n        )\n        return winreg.HKEY_LOCAL_MACHINE", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "verbose", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "verbose = 1\nroot_key_name = \"Software\\\\Python\\\\PythonCore\\\\\" + sys.winver\ndef get_root_hkey():\n    try:\n        winreg.OpenKey(\n            winreg.HKEY_LOCAL_MACHINE, root_key_name, 0, winreg.KEY_CREATE_SUB_KEY\n        )\n        return winreg.HKEY_LOCAL_MACHINE\n    except OSError:\n        # Either not exist, or no permissions to create subkey means", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "root_key_name", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "root_key_name = \"Software\\\\Python\\\\PythonCore\\\\\" + sys.winver\ndef get_root_hkey():\n    try:\n        winreg.OpenKey(\n            winreg.HKEY_LOCAL_MACHINE, root_key_name, 0, winreg.KEY_CREATE_SUB_KEY\n        )\n        return winreg.HKEY_LOCAL_MACHINE\n    except OSError:\n        # Either not exist, or no permissions to create subkey means\n        # must be HKCU", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "run_test", "kind": 2, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "def run_test(script, cmdline_extras):\n    dirname, scriptname = os.path.split(script)\n    # some tests prefer to be run from their directory.\n    cmd = [sys.executable, \"-u\", scriptname] + cmdline_extras\n    print(\"--- Running '%s' ---\" % script)\n    sys.stdout.flush()\n    result = subprocess.run(cmd, check=False, cwd=dirname)\n    print(f\"*** Test script '{script}' exited with {result.returncode}\")\n    sys.stdout.flush()\n    if result.returncode:", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "find_and_run", "kind": 2, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "def find_and_run(possible_locations, extras):\n    for maybe in possible_locations:\n        if os.path.isfile(maybe):\n            run_test(maybe, extras)\n            break\n    else:\n        raise RuntimeError(\n            \"Failed to locate a test script in one of %s\" % possible_locations\n        )\ndef main():", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "main", "kind": 2, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "def main():\n    import argparse\n    code_directories = [project_root] + site_packages\n    parser = argparse.ArgumentParser(\n        description=\"A script to trigger tests in all subprojects of PyWin32.\"\n    )\n    parser.add_argument(\n        \"-no-user-interaction\",\n        default=False,\n        action=\"store_true\",", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "project_root", "kind": 5, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))\nsite_packages = [site.getusersitepackages()] + site.getsitepackages()\nfailures = []\n# Run a test using subprocess and wait for the result.\n# If we get an returncode != 0, we know that there was an error, but we don't\n# abort immediately - we run as many tests as we can.\ndef run_test(script, cmdline_extras):\n    dirname, scriptname = os.path.split(script)\n    # some tests prefer to be run from their directory.\n    cmd = [sys.executable, \"-u\", scriptname] + cmdline_extras", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "site_packages", "kind": 5, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "site_packages = [site.getusersitepackages()] + site.getsitepackages()\nfailures = []\n# Run a test using subprocess and wait for the result.\n# If we get an returncode != 0, we know that there was an error, but we don't\n# abort immediately - we run as many tests as we can.\ndef run_test(script, cmdline_extras):\n    dirname, scriptname = os.path.split(script)\n    # some tests prefer to be run from their directory.\n    cmd = [sys.executable, \"-u\", scriptname] + cmdline_extras\n    print(\"--- Running '%s' ---\" % script)", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "failures", "kind": 5, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "failures = []\n# Run a test using subprocess and wait for the result.\n# If we get an returncode != 0, we know that there was an error, but we don't\n# abort immediately - we run as many tests as we can.\ndef run_test(script, cmdline_extras):\n    dirname, scriptname = os.path.split(script)\n    # some tests prefer to be run from their directory.\n    cmd = [sys.executable, \"-u\", scriptname] + cmdline_extras\n    print(\"--- Running '%s' ---\" % script)\n    sys.stdout.flush()", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "analyze_chunking_behavior", "kind": 2, "importPath": "debug_chunker", "description": "debug_chunker", "peekOfCode": "def analyze_chunking_behavior(text):\n    \"\"\"分析分块行为\"\"\"\n    print(\"🔍 分析SDPM Chunker的分块行为\")\n    print(\"=\" * 60)\n    # 初始化tokenizer来计算token\n    encoding = tiktoken.get_encoding(\"cl100k_base\")\n    # 分析原始文本\n    total_tokens = len(encoding.encode(text))\n    sentences = text.split('。')  # 简单的句子分割\n    print(f\"📄 原始文本信息:\")", "detail": "debug_chunker", "documentation": {}}, {"label": "test_with_your_text", "kind": 2, "importPath": "debug_chunker", "description": "debug_chunker", "peekOfCode": "def test_with_your_text():\n    \"\"\"测试你的具体文本\"\"\"\n    # 这里放入你在notebook中使用的文本\n    # 你可以把你的文本复制到这里\n    test_text = \"\"\"\n    请把你在notebook中使用的文本粘贴到这里，\n    这样我们就能具体分析为什么会出现780 token的分块了。\n    \"\"\"\n    if \"请把你在notebook\" in test_text:\n        print(\"⚠️  请在test_text变量中放入你的实际文本内容\")", "detail": "debug_chunker", "documentation": {}}, {"label": "create_sample_text_file", "kind": 2, "importPath": "sdpm_chunker_example", "description": "sdpm_chunker_example", "peekOfCode": "def create_sample_text_file():\n    \"\"\"创建一个示例txt文件用于测试\"\"\"\n    sample_text = \"\"\"人工智能是计算机科学的一个分支，它试图理解智能的实质。\n机器学习是人工智能的一个重要子领域。\n深度学习使用神经网络来模拟人脑的工作方式。\n神经网络通过多层处理来学习复杂的模式。\n训练数据对于模型性能至关重要。\nGPU显著加速神经网络的计算过程。\n高质量的训练数据能够提高模型的准确性。\n数据预处理是训练过程中的关键步骤。", "detail": "sdpm_chunker_example", "documentation": {}}, {"label": "chunk_txt_file", "kind": 2, "importPath": "sdpm_chunker_example", "description": "sdpm_chunker_example", "peekOfCode": "def chunk_txt_file(file_path, chunker):\n    \"\"\"\n    读取txt文件并使用SDPM Chunker进行分块\n    Args:\n        file_path: txt文件路径\n        chunker: SDPM Chunker实例\n    Returns:\n        chunks: 分块结果列表\n    \"\"\"\n    try:", "detail": "sdpm_chunker_example", "documentation": {}}, {"label": "print_all_chunks", "kind": 2, "importPath": "sdpm_chunker_example", "description": "sdpm_chunker_example", "peekOfCode": "def print_all_chunks(chunks):\n    \"\"\"\n    打印所有分块的内容\n    Args:\n        chunks: 分块结果列表\n    \"\"\"\n    if not chunks:\n        print(\"没有生成任何分块\")\n        return\n    print(f\"总共生成了 {len(chunks)} 个分块\")", "detail": "sdpm_chunker_example", "documentation": {}}, {"label": "print_detailed_sentences", "kind": 2, "importPath": "sdpm_chunker_example", "description": "sdpm_chunker_example", "peekOfCode": "def print_detailed_sentences(chunks):\n    \"\"\"\n    打印每个分块中句子的详细信息\n    Args:\n        chunks: 分块结果列表\n    \"\"\"\n    print(\"\\n🔍 详细句子信息:\")\n    print(\"=\" * 100)\n    for i, chunk in enumerate(chunks, 1):\n        print(f\"\\n📄 分块 {i} 的句子详情:\")", "detail": "sdpm_chunker_example", "documentation": {}}, {"label": "batch_chunk_files", "kind": 2, "importPath": "sdpm_chunker_example", "description": "sdpm_chunker_example", "peekOfCode": "def batch_chunk_files(file_paths, chunker):\n    \"\"\"\n    批量处理多个txt文件\n    Args:\n        file_paths: 文件路径列表\n        chunker: SDPM Chunker实例\n    Returns:\n        all_chunks: 所有文件的分块结果\n    \"\"\"\n    texts = []", "detail": "sdpm_chunker_example", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "sdpm_chunker_example", "description": "sdpm_chunker_example", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"🚀 SDPM Chunker 文档分块示例\")\n    print(\"=\" * 50)\n    # 初始化SDPM Chunker\n    print(\"🔧 正在初始化SDPM Chunker...\")\n    try:\n        chunker = SDPMChunker(\n            embedding_model=\"minishlab/potion-base-8M\",  # 默认模型\n            threshold=0.5,                              # 相似度阈值", "detail": "sdpm_chunker_example", "documentation": {}}, {"label": "create_test_documents", "kind": 2, "importPath": "sdpm_vs_other_chunkers", "description": "sdpm_vs_other_chunkers", "peekOfCode": "def create_test_documents():\n    \"\"\"创建不同类型的测试文档来展示SDPM的优势\"\"\"\n    # 文档1：主题分散但相关的文档（SDPM的强项）\n    scattered_topics = \"\"\"\n    机器学习是人工智能的重要分支。它通过算法从数据中学习模式。\n    在医疗领域，AI诊断系统正在快速发展。医生可以利用AI辅助诊断疾病。\n    深度学习使用神经网络模拟人脑。多层神经网络能够学习复杂特征。\n    AI在医疗影像分析中表现出色。X光片、CT扫描都可以用AI分析。\n    神经网络的训练需要大量数据。数据质量直接影响模型性能。\n    远程医疗结合AI技术，让偏远地区也能享受优质医疗服务。", "detail": "sdpm_vs_other_chunkers", "documentation": {}}, {"label": "compare_chunkers", "kind": 2, "importPath": "sdpm_vs_other_chunkers", "description": "sdpm_vs_other_chunkers", "peekOfCode": "def compare_chunkers(text, title):\n    \"\"\"对比不同分块器的效果\"\"\"\n    print(f\"\\n{'='*60}\")\n    print(f\"📄 测试文档: {title}\")\n    print(f\"{'='*60}\")\n    # 初始化不同的分块器\n    chunkers = {\n        \"Token分块器\": TokenChunker(chunk_size=150, chunk_overlap=20),\n        \"句子分块器\": SentenceChunker(chunk_size=150),\n        \"SDPM分块器\": SDPMChunker(", "detail": "sdpm_vs_other_chunkers", "documentation": {}}, {"label": "analyze_semantic_grouping", "kind": 2, "importPath": "sdpm_vs_other_chunkers", "description": "sdpm_vs_other_chunkers", "peekOfCode": "def analyze_semantic_grouping():\n    \"\"\"分析SDPM的语义分组能力\"\"\"\n    print(f\"\\n{'='*60}\")\n    print(\"🧠 SDPM语义分组分析\")\n    print(f\"{'='*60}\")\n    # 创建一个主题混合的文档\n    mixed_content = \"\"\"\n    Python是一种编程语言。它语法简洁，易于学习。\n    今天天气很好，阳光明媚。适合出去散步。\n    JavaScript用于网页开发。它可以创建交互式网页。", "detail": "sdpm_vs_other_chunkers", "documentation": {}}, {"label": "demonstrate_sdpm_value", "kind": 2, "importPath": "sdpm_vs_other_chunkers", "description": "sdpm_vs_other_chunkers", "peekOfCode": "def demonstrate_sdpm_value():\n    \"\"\"演示SDPM的真正价值\"\"\"\n    print(\"🚀 SDPM Chunker 价值演示\")\n    print(\"=\"*60)\n    print(\"\\n💡 SDMP Chunker的独特价值:\")\n    print(\"1. 🎯 语义感知: 能识别内容的语义相关性\")\n    print(\"2. 🔗 智能合并: 将分散但相关的内容组合在一起\")\n    print(\"3. 🧠 上下文保持: 保持相关概念的连贯性\")\n    print(\"4. 📊 跨距离关联: 能够连接距离较远但相关的内容\")\n    # 创建测试文档", "detail": "sdpm_vs_other_chunkers", "documentation": {}}, {"label": "simple_chunk_test", "kind": 2, "importPath": "simple_chunker_test", "description": "simple_chunker_test", "peekOfCode": "def simple_chunk_test():\n    \"\"\"简单的分块测试\"\"\"\n    # 测试文本\n    text = \"\"\"人工智能是计算机科学的一个分支。它试图理解智能的实质。\n机器学习是人工智能的一个重要子领域。深度学习使用神经网络来模拟人脑的工作方式。\n神经网络通过多层处理来学习复杂的模式。训练数据对于模型性能至关重要。\nGPU显著加速神经网络的计算过程。高质量的训练数据能够提高模型的准确性。\n自然语言处理是人工智能的另一个重要应用领域。计算机视觉让机器能够理解和分析图像。\n强化学习通过奖励机制来训练智能体。机器学习算法可以从数据中自动学习模式。\"\"\"\n    print(\"🚀 开始SDPM Chunker测试\")", "detail": "simple_chunker_test", "documentation": {}}, {"label": "chunk_your_file", "kind": 2, "importPath": "simple_chunker_test", "description": "simple_chunker_test", "peekOfCode": "def chunk_your_file(file_path):\n    \"\"\"分块你自己的文件\"\"\"\n    print(f\"📖 读取文件: {file_path}\")\n    try:\n        with open(file_path, 'r', encoding='utf-8') as f:\n            text = f.read()\n    except Exception as e:\n        print(f\"❌ 读取文件失败: {e}\")\n        return\n    print(f\"📄 文件长度: {len(text)} 字符\")", "detail": "simple_chunker_test", "documentation": {}}, {"label": "explain_your_confusion", "kind": 2, "importPath": "why_sdpm_seems_useless", "description": "why_sdpm_seems_useless", "peekOfCode": "def explain_your_confusion():\n    \"\"\"解释你遇到的情况\"\"\"\n    print(\"🤔 为什么你觉得SDPM没什么用？\")\n    print(\"=\"*50)\n    print(\"\\n📄 你的文档特点:\")\n    print(\"• 是一篇连续的随笔/思考\")\n    print(\"• 虽然都在讲AI，但是是流水账式的\")\n    print(\"• 没有重复的概念需要合并\")\n    print(\"• 内容本身就是连贯的\")\n    print(\"\\n🎯 SDPM的设计目的:\")", "detail": "why_sdpm_seems_useless", "documentation": {}}, {"label": "create_sdpm_showcase", "kind": 2, "importPath": "why_sdpm_seems_useless", "description": "why_sdpm_seems_useless", "peekOfCode": "def create_sdpm_showcase():\n    \"\"\"创建一个能展示SDPM优势的例子\"\"\"\n    print(f\"\\n{'='*60}\")\n    print(\"🚀 SDPM真正的用武之地\")\n    print(\"=\"*60)\n    # 这种文档SDPM会很有用\n    technical_doc = \"\"\"\n    机器学习基础概念介绍。\n    数据预处理是第一步。需要清洗和标准化数据。\n    Python是最流行的编程语言之一。", "detail": "why_sdpm_seems_useless", "documentation": {}}, {"label": "show_real_world_example", "kind": 2, "importPath": "why_sdpm_seems_useless", "description": "why_sdpm_seems_useless", "peekOfCode": "def show_real_world_example():\n    \"\"\"展示真实世界的应用场景\"\"\"\n    print(f\"\\n{'='*60}\")\n    print(\"🌍 SDPM的真实应用场景\")\n    print(\"=\"*60)\n    scenarios = [\n        {\n            \"场景\": \"📚 技术文档\",\n            \"描述\": \"API文档中，同一个概念在多个章节出现\",\n            \"SDPM优势\": \"将相关的API说明合并在一起\"", "detail": "why_sdpm_seems_useless", "documentation": {}}, {"label": "conclusion", "kind": 2, "importPath": "why_sdpm_seems_useless", "description": "why_sdpm_seems_useless", "peekOfCode": "def conclusion():\n    \"\"\"总结\"\"\"\n    print(f\"\\n{'='*60}\")\n    print(\"🎯 总结\")\n    print(\"=\"*60)\n    print(\"\\n🤔 你的困惑很正常！\")\n    print(\"因为你选择的测试文档不适合展示SDPM的优势。\")\n    print(\"\\n📄 你的文档特点:\")\n    print(\"• 连续叙述，内容本身连贯\")\n    print(\"• 没有重复概念需要合并\")", "detail": "why_sdpm_seems_useless", "documentation": {}}]