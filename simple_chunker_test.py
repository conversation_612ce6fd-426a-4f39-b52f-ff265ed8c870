#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版SDPM Chunker测试代码
快速测试文档分块功能
"""

from chonkie import SDPMChunker

def simple_chunk_test():
    """简单的分块测试"""
    
    # 测试文本
    text = """人工智能是计算机科学的一个分支。它试图理解智能的实质。
机器学习是人工智能的一个重要子领域。深度学习使用神经网络来模拟人脑的工作方式。

神经网络通过多层处理来学习复杂的模式。训练数据对于模型性能至关重要。
GPU显著加速神经网络的计算过程。高质量的训练数据能够提高模型的准确性。

自然语言处理是人工智能的另一个重要应用领域。计算机视觉让机器能够理解和分析图像。
强化学习通过奖励机制来训练智能体。机器学习算法可以从数据中自动学习模式。"""

    print("🚀 开始SDPM Chunker测试")
    print("=" * 50)
    
    # 初始化chunker
    print("🔧 初始化SDPM Chunker...")
    chunker = SDPMChunker(
        embedding_model="minishlab/potion-base-8M",
        threshold=0.5,
        chunk_size=256,  # 小块大小便于观察
        min_sentences=1,
        skip_window=1
    )
    print("✅ 初始化完成!")
    
    # 进行分块
    print("\n📝 开始分块...")
    chunks = chunker.chunk(text)
    
    # 打印结果
    print(f"\n📊 生成了 {len(chunks)} 个分块:")
    print("=" * 60)
    
    for i, chunk in enumerate(chunks, 1):
        print(f"\n🔸 分块 {i}:")
        print(f"   内容: {chunk.text}")
        print(f"   Token数: {chunk.token_count}")
        print(f"   句子数: {len(chunk.sentences)}")
        print("-" * 60)
    
    return chunks

def chunk_your_file(file_path):
    """分块你自己的文件"""
    
    print(f"📖 读取文件: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            text = f.read()
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return
    
    print(f"📄 文件长度: {len(text)} 字符")
    
    # 初始化chunker
    chunker = SDPMChunker(
        threshold=0.5,
        chunk_size=512,
        min_sentences=1
    )
    
    # 分块
    chunks = chunker.chunk(text)
    
    # 打印所有分块
    print(f"\n📊 总共 {len(chunks)} 个分块:")
    print("=" * 80)
    
    for i, chunk in enumerate(chunks, 1):
        print(f"\n📄 分块 {i}:")
        print(f"📝 {chunk.text}")
        print(f"🔢 Token数: {chunk.token_count}")
        print(f"📊 句子数: {len(chunk.sentences)}")
        print("-" * 80)

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # 如果提供了文件路径，处理该文件
        file_path = sys.argv[1]
        chunk_your_file(file_path)
    else:
        # 否则运行简单测试
        simple_chunk_test()
        
        print("\n💡 提示:")
        print("   要处理你自己的文件，请运行:")
        print("   python simple_chunker_test.py your_file.txt")
