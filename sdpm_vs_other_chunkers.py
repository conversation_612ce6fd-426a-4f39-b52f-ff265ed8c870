#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比不同分块器的效果，展示SDPM Chunker的真正价值
"""

from chonkie import SDPMChunker, TokenChunker, SentenceChunker
import tiktoken

def create_test_documents():
    """创建不同类型的测试文档来展示SDPM的优势"""
    
    # 文档1：主题分散但相关的文档（SDPM的强项）
    scattered_topics = """
    机器学习是人工智能的重要分支。它通过算法从数据中学习模式。
    
    在医疗领域，AI诊断系统正在快速发展。医生可以利用AI辅助诊断疾病。
    
    深度学习使用神经网络模拟人脑。多层神经网络能够学习复杂特征。
    
    AI在医疗影像分析中表现出色。X光片、CT扫描都可以用AI分析。
    
    神经网络的训练需要大量数据。数据质量直接影响模型性能。
    
    远程医疗结合AI技术，让偏远地区也能享受优质医疗服务。
    
    机器学习算法包括监督学习、无监督学习和强化学习。每种都有不同应用场景。
    
    AI辅助手术机器人提高了手术精度。外科医生可以更精确地操作。
    """
    
    # 文档2：连续叙述的文档（传统分块器也能处理）
    continuous_narrative = """
    今天早上我去了咖啡店。店里人很多，排队等了十分钟。
    点了一杯拿铁和一个可颂。咖啡师很友好，还画了拉花。
    坐在窗边的位置，看着外面的街景。有人在遛狗，有人在跑步。
    喝完咖啡后，我去了附近的书店。书店里很安静，适合阅读。
    买了一本关于心理学的书。回家的路上遇到了老朋友。
    我们聊了很久，约定下次一起吃饭。回到家已经是下午了。
    """
    
    return scattered_topics, continuous_narrative

def compare_chunkers(text, title):
    """对比不同分块器的效果"""
    
    print(f"\n{'='*60}")
    print(f"📄 测试文档: {title}")
    print(f"{'='*60}")
    
    # 初始化不同的分块器
    chunkers = {
        "Token分块器": TokenChunker(chunk_size=150, chunk_overlap=20),
        "句子分块器": SentenceChunker(chunk_size=150),
        "SDPM分块器": SDPMChunker(
            chunk_size=150, 
            threshold=0.6,  # 较高阈值，更容易合并相关内容
            skip_window=2   # 允许跨越更多块寻找相关内容
        )
    }
    
    encoding = tiktoken.get_encoding("cl100k_base")
    
    for name, chunker in chunkers.items():
        print(f"\n🔧 {name}:")
        print("-" * 40)
        
        try:
            chunks = chunker.chunk(text)
            print(f"生成分块数: {len(chunks)}")
            
            for i, chunk in enumerate(chunks, 1):
                chunk_tokens = len(encoding.encode(chunk.text))
                print(f"\n  📄 分块 {i} ({chunk_tokens} tokens):")
                # 只显示前100个字符
                preview = chunk.text.replace('\n', ' ').strip()[:100]
                print(f"     {preview}...")
                
                # 如果是SDPM，显示语义相关性信息
                if name == "SDPM分块器" and hasattr(chunk, 'sentences'):
                    topics = []
                    for sentence in chunk.sentences:
                        if "机器学习" in sentence.text or "AI" in sentence.text or "神经网络" in sentence.text:
                            topics.append("AI/ML")
                        elif "医疗" in sentence.text or "医生" in sentence.text or "诊断" in sentence.text:
                            topics.append("医疗")
                        elif "数据" in sentence.text:
                            topics.append("数据")
                    
                    if topics:
                        unique_topics = list(set(topics))
                        print(f"     🎯 包含主题: {', '.join(unique_topics)}")
                        
        except Exception as e:
            print(f"     ❌ 错误: {e}")

def analyze_semantic_grouping():
    """分析SDPM的语义分组能力"""
    
    print(f"\n{'='*60}")
    print("🧠 SDPM语义分组分析")
    print(f"{'='*60}")
    
    # 创建一个主题混合的文档
    mixed_content = """
    Python是一种编程语言。它语法简洁，易于学习。
    
    今天天气很好，阳光明媚。适合出去散步。
    
    JavaScript用于网页开发。它可以创建交互式网页。
    
    我喜欢听音乐，特别是古典音乐。贝多芬是我最喜欢的作曲家。
    
    React是一个JavaScript框架。它用于构建用户界面。
    
    春天来了，花儿都开了。公园里很多人在赏花。
    
    编程需要逻辑思维。解决问题是编程的核心。
    
    音乐能够陶冶情操。不同的音乐风格有不同的魅力。
    """
    
    # 使用SDPM分块
    sdpm_chunker = SDPMChunker(
        chunk_size=120,
        threshold=0.7,  # 高阈值，只合并非常相似的内容
        skip_window=3
    )
    
    chunks = sdpm_chunker.chunk(mixed_content)
    
    print(f"SDPM生成了 {len(chunks)} 个分块:")
    
    for i, chunk in enumerate(chunks, 1):
        print(f"\n📄 分块 {i}:")
        print(f"   内容: {chunk.text.replace(chr(10), ' ').strip()}")
        
        # 分析主题
        topics = []
        if any(word in chunk.text for word in ["Python", "JavaScript", "React", "编程"]):
            topics.append("编程")
        if any(word in chunk.text for word in ["天气", "散步", "春天", "花"]):
            topics.append("自然/天气")
        if any(word in chunk.text for word in ["音乐", "贝多芬", "古典"]):
            topics.append("音乐")
            
        if topics:
            print(f"   🎯 主题: {', '.join(topics)}")
        else:
            print(f"   🎯 主题: 混合/其他")

def demonstrate_sdpm_value():
    """演示SDPM的真正价值"""
    
    print("🚀 SDPM Chunker 价值演示")
    print("="*60)
    
    print("\n💡 SDMP Chunker的独特价值:")
    print("1. 🎯 语义感知: 能识别内容的语义相关性")
    print("2. 🔗 智能合并: 将分散但相关的内容组合在一起")
    print("3. 🧠 上下文保持: 保持相关概念的连贯性")
    print("4. 📊 跨距离关联: 能够连接距离较远但相关的内容")
    
    # 创建测试文档
    scattered_topics, continuous_narrative = create_test_documents()
    
    # 对比测试
    compare_chunkers(scattered_topics, "主题分散的AI文档 (SDPM的强项)")
    compare_chunkers(continuous_narrative, "连续叙述文档 (传统方法也够用)")
    
    print(f"\n{'='*60}")
    print("📊 总结对比:")
    print("="*60)
    print("🔸 Token分块器: 严格按token数切分，可能切断语义")
    print("🔸 句子分块器: 按句子分组，但不考虑语义相关性")  
    print("🔸 SDPM分块器: 智能识别并合并语义相关的内容")
    print("\n💭 SDPM最适合的场景:")
    print("   • 技术文档 (概念分散但相关)")
    print("   • 学术论文 (主题在不同段落中重复)")
    print("   • 知识库文档 (相关信息分布在各处)")
    print("   • 复杂报告 (同一主题多次出现)")

if __name__ == "__main__":
    demonstrate_sdpm_value()
    
    print(f"\n{'='*60}")
    print("🎯 针对你的文档的建议:")
    print("="*60)
    print("你的《人工智能.txt》是一篇连续的随笔，")
    print("主题虽然都是AI，但是是流水账式的思考，")
    print("这种情况下SDPM的优势确实不明显。")
    print("\nSDMP更适合:")
    print("• 技术手册 (同一概念在多处出现)")
    print("• 研究报告 (相关数据分散在各章节)")  
    print("• 知识库 (相关概念需要组合在一起)")
    print("\n💡 建议: 对于你这种文档，普通的SentenceChunker就够用了！")
